# run again.  Do not edit this file unless you know what you are doing.


from PySide6 import QtCore, QtWidgets
from PySide6.QtCore import Qt, QMetaObject

from videotrans import tts
from videotrans.component.component import Textedit
from videotrans.configure import config
from videotrans.configure.config import box_lang


class Ui_peiyin(object):
    def setupUi(self, peiyin):
        self.has_done = False
        if not peiyin.objectName():
            peiyin.setObjectName(u"peiyin")
        peiyin.resize(800, 500)
        peiyin.setWindowModality(QtCore.Qt.NonModal)

        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(peiyin.sizePolicy().hasHeightForWidth())
        peiyin.setSizePolicy(sizePolicy)

        self.hecheng_files = []
        self.error_msg = ""

        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(peiyin)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout()
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.hecheng_layout = QtWidgets.QVBoxLayout()
        self.hecheng_layout.setObjectName("hecheng_layout")

        self.hecheng_plaintext = Textedit()
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.hecheng_plaintext.sizePolicy().hasHeightForWidth())
        self.hecheng_plaintext.setSizePolicy(sizePolicy)
        self.hecheng_plaintext.setMinimumSize(0, 150)
        self.hecheng_plaintext.setPlaceholderText(config.transobj['tuodonghuoshuru'])
        self.hecheng_importbtn = QtWidgets.QPushButton()
        self.hecheng_importbtn.setObjectName("hecheng_importbtn")
        self.hecheng_importbtn.setFixedHeight(150)
        self.hecheng_importbtn.setCursor(Qt.PointingHandCursor)

        self.hecheng_importbtn.setText(config.box_lang['Import text to be translated from a file..'])

        self.hecheng_layout.insertWidget(0, self.hecheng_importbtn)
        self.hecheng_layout.insertWidget(1, self.hecheng_plaintext)
        self.verticalLayout_4.addLayout(self.hecheng_layout)

        self.horizontalLayout_10 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_10.setObjectName("horizontalLayout_10")
        self.horizontalLayout_10_1 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_10_1.setObjectName("horizontalLayout_10_1")
        self.formLayout_3 = QtWidgets.QFormLayout()
        self.formLayout_3.setFormAlignment(QtCore.Qt.AlignLeading | QtCore.Qt.AlignLeft | QtCore.Qt.AlignVCenter)
        self.formLayout_3.setObjectName("formLayout_3")
        self.label_10 = QtWidgets.QLabel()
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_10.sizePolicy().hasHeightForWidth())
        self.label_10.setSizePolicy(sizePolicy)
        self.label_10.setMinimumSize(QtCore.QSize(0, 30))
        self.label_10.setObjectName("label_10")
        self.formLayout_3.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label_10)
        self.hecheng_language = QtWidgets.QComboBox()
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.hecheng_language.sizePolicy().hasHeightForWidth())
        self.hecheng_language.setSizePolicy(sizePolicy)
        self.hecheng_language.setMinimumSize(QtCore.QSize(0, 30))
        self.hecheng_language.setObjectName("hecheng_language")
        #self.hecheng_language.addItems(['-'] + config.langnamelist)

        self.formLayout_3.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.hecheng_language)
        self.horizontalLayout_10.addLayout(self.formLayout_3)
        self.formLayout_7 = QtWidgets.QFormLayout()
        self.formLayout_7.setFormAlignment(QtCore.Qt.AlignLeading | QtCore.Qt.AlignLeft | QtCore.Qt.AlignVCenter)
        self.formLayout_7.setObjectName("formLayout_7")
        self.label_8 = QtWidgets.QLabel()
        self.label_8.setMinimumSize(QtCore.QSize(0, 30))
        self.label_8.setObjectName("label_8")
        self.formLayout_7.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label_8)
        self.tts_type = QtWidgets.QComboBox()
        self.tts_type.setMinimumSize(QtCore.QSize(0, 30))
        self.tts_type.setObjectName("tts_type")
        self.tts_type.addItems(tts.TTS_NAME_LIST)
        self.formLayout_7.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.tts_type)
        self.horizontalLayout_10.addLayout(self.formLayout_7)
        self.formLayout_4 = QtWidgets.QFormLayout()
        self.formLayout_4.setFormAlignment(QtCore.Qt.AlignLeading | QtCore.Qt.AlignLeft | QtCore.Qt.AlignVCenter)
        self.formLayout_4.setObjectName("formLayout_4")
        self.label_11 = QtWidgets.QLabel()
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_11.sizePolicy().hasHeightForWidth())
        self.label_11.setSizePolicy(sizePolicy)
        self.label_11.setMinimumSize(QtCore.QSize(0, 30))
        self.label_11.setObjectName("label_11")
        self.formLayout_4.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label_11)
        self.hecheng_role = QtWidgets.QComboBox()
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.hecheng_role.sizePolicy().hasHeightForWidth())
        self.hecheng_role.setSizePolicy(sizePolicy)
        self.hecheng_role.setMinimumSize(QtCore.QSize(0, 30))
        self.hecheng_role.setObjectName("hecheng_role")
        self.hecheng_role.addItems(['No'])
        self.formLayout_4.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.hecheng_role)
        self.horizontalLayout_10.addLayout(self.formLayout_4)

        self.listen_btn = QtWidgets.QPushButton()
        self.listen_btn.setFixedWidth(80)
        self.listen_btn.setToolTip(config.uilanglist.get("shuoming01"))
        self.listen_btn.setText(config.uilanglist.get("Trial dubbing"))
        self.horizontalLayout_10.addWidget(self.listen_btn)

        self.formLayout_5 = QtWidgets.QFormLayout()
        self.formLayout_5.setFormAlignment(QtCore.Qt.AlignLeading | QtCore.Qt.AlignLeft | QtCore.Qt.AlignVCenter)
        self.formLayout_5.setObjectName("formLayout_5")
        self.label_12 = QtWidgets.QLabel()
        self.label_12.setMinimumSize(QtCore.QSize(0, 30))
        self.label_12.setObjectName("label_12")
        self.formLayout_5.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label_12)
        self.hecheng_rate = QtWidgets.QSpinBox()
        self.hecheng_rate.setMinimum(-100)
        self.hecheng_rate.setMaximum(100)
        self.hecheng_rate.setMinimumWidth(90)
        self.hecheng_rate.setObjectName("hecheng_rate")
        self.formLayout_5.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.hecheng_rate)
        self.horizontalLayout_10_1.addLayout(self.formLayout_5)

        self.voice_autorate = QtWidgets.QCheckBox()
        self.voice_autorate.setObjectName("voice_autorate")

        self.horizontalLayout_10_1.addWidget(self.voice_autorate)

        self.edge_volume_layout = QtWidgets.QHBoxLayout()

        self.volume_label = QtWidgets.QLabel()
        self.volume_label.setText("音量+" if config.defaulelang == 'zh' else "Volume+")

        self.volume_rate = QtWidgets.QSpinBox()
        self.volume_rate.setMinimum(-95)
        self.volume_rate.setMaximum(100)
        self.volume_rate.setMinimumWidth(90)
        self.volume_rate.setObjectName("volume_rate")

        self.pitch_label = QtWidgets.QLabel()
        self.pitch_label.setText("音调+" if config.defaulelang == 'zh' else "Pitch+")
        self.pitch_rate = QtWidgets.QSpinBox()
        self.pitch_rate.setMinimum(-100)
        self.pitch_rate.setMaximum(100)
        self.pitch_rate.setMinimumWidth(90)
        self.pitch_rate.setObjectName("pitch_rate")

        self.out_format_label = QtWidgets.QLabel(text='输出格式' if config.defaulelang == 'zh' else 'Out format')

        self.out_format = QtWidgets.QComboBox()
        self.out_format.addItems([
            'wav',
            "mp3",
            "m4a"
        ])
        
        self.save_to_srt=QtWidgets.QCheckBox()
        

        self.edge_volume_layout.addWidget(self.volume_label)
        self.edge_volume_layout.addWidget(self.volume_rate)
        self.edge_volume_layout.addWidget(self.pitch_label)
        self.edge_volume_layout.addWidget(self.pitch_rate)
        self.edge_volume_layout.addWidget(self.out_format_label)
        self.edge_volume_layout.addWidget(self.out_format)
        self.edge_volume_layout.addWidget(self.save_to_srt)

        self.horizontalLayout_10_1.addLayout(self.edge_volume_layout)

        self.hecheng_startbtn = QtWidgets.QPushButton()
        self.hecheng_startbtn.setMinimumSize(QtCore.QSize(200, 40))
        self.hecheng_startbtn.setObjectName("hecheng_startbtn")
        self.hecheng_startbtn.setCursor(Qt.PointingHandCursor)

        self.hecheng_stop = QtWidgets.QPushButton()
        self.hecheng_stop.setFixedWidth(100)
        self.hecheng_stop.setText('停止' if config.defaulelang=='zh' else 'Stop')
        self.hecheng_stop.setDisabled(True)
        self.hecheng_stop.setObjectName("hecheng_stop")
        self.hecheng_stop.setCursor(Qt.PointingHandCursor)

        h1=QtWidgets.QHBoxLayout()
        h1.addWidget(self.hecheng_startbtn)
        h1.addWidget(self.hecheng_stop)



        self.verticalLayout_4.addLayout(self.horizontalLayout_10)
        self.verticalLayout_4.addLayout(self.horizontalLayout_10_1)

        self.verticalLayout_4.addLayout(h1)

        self.loglabel = QtWidgets.QPushButton()
        self.loglabel.setStyleSheet('''color:#148cd2;background-color:transparent''')
        self.verticalLayout_4.addWidget(self.loglabel)

        self.gridLayout_3 = QtWidgets.QGridLayout()
        self.gridLayout_3.setObjectName("gridLayout_3")
        self.hecheng_opendir = QtWidgets.QPushButton()
        self.hecheng_opendir.setStyleSheet("""background-color:transparent""")
        self.hecheng_opendir.setObjectName("hecheng_opendir")
        self.hecheng_opendir.setMinimumSize(QtCore.QSize(100, 35))
        self.hecheng_opendir.setCursor(Qt.PointingHandCursor)
        self.verticalLayout_4.addWidget(self.hecheng_opendir)
        self.horizontalLayout_11.addLayout(self.verticalLayout_4)

        self.retranslateUi(peiyin)

        # tab-4 语音合成

        QMetaObject.connectSlotsByName(peiyin)

    def retranslateUi(self, peiyin):
        peiyin.setWindowTitle(config.uilanglist.get("From  Text  Into  Speech"))

        self.label_10.setText(box_lang.get("Subtitle lang"))
        self.label_8.setText("TTS" if config.defaulelang != 'zh' else '配音渠道')
        self.label_11.setText(box_lang.get("Select role"))
        self.label_12.setText(box_lang.get("Speed change"))
        self.hecheng_rate.setToolTip(box_lang.get("Negative deceleration, positive acceleration"))
        self.voice_autorate.setText(box_lang.get("Automatic acceleration?"))
        self.save_to_srt.setText("Save to original location" if config.defaulelang != 'zh' else '保存到原位置')
        self.save_to_srt.setToolTip("If checked, the synthesized audio is saved to the original folder where the srt file is located." if config.defaulelang != 'zh' else '如果选中，则合成后音频保存到srt文件所在原文件夹内')
        self.hecheng_startbtn.setText(box_lang.get("Start"))
        self.hecheng_opendir.setText(box_lang.get("Open dir"))
