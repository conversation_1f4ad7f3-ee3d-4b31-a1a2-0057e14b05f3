# run again.  Do not edit this file unless you know what you are doing.
from PySide6 import QtWidgets, QtCore
from PySide6.QtCore import QMetaObject, QRect, QSize, Qt
from PySide6.QtWidgets import QLabel, QLineEdit, QPlainTextEdit, QPushButton, QSizePolicy

from videotrans.configure import config
from videotrans.util import tools


class Ui_ttsapiform(object):
    def setupUi(self, ttsapiform):
        self.has_done = False
        if not ttsapiform.objectName():
            ttsapiform.setObjectName("ttsapiform")
        ttsapiform.setWindowModality(Qt.NonModal)
        ttsapiform.resize(600, 600)
        sizePolicy = QSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(ttsapiform.sizePolicy().hasHeightForWidth())
        ttsapiform.setSizePolicy(sizePolicy)
        ttsapiform.setMaximumSize(QSize(600, 600))

        v1=QtWidgets.QVBoxLayout(ttsapiform)
        self.label = QLabel()
        self.label.setObjectName("label")
        self.label.setMinimumSize(QSize(0, 35))
        self.api_url = QLineEdit(ttsapiform)
        self.api_url.setObjectName("api_url")
        self.api_url.setMinimumSize(QSize(0, 35))

        h1=QtWidgets.QHBoxLayout()
        h1.addWidget(self.label)
        h1.addWidget(self.api_url)
        v1.addLayout(h1)

        h2=QtWidgets.QHBoxLayout()
        self.label_2 = QLabel(ttsapiform)
        self.label_2.setObjectName("label_2")
        self.label_2.setMinimumSize(QSize(0, 35))
        self.label_2.setSizeIncrement(QSize(0, 35))
        self.voice_role = QPlainTextEdit(ttsapiform)
        self.voice_role.setObjectName("voice_role")
        self.voice_role.setMinimumSize(QSize(0, 35))
        h2.addWidget(self.label_2)
        h2.addWidget(self.voice_role)
        v1.addLayout(h2)

        h3=QtWidgets.QHBoxLayout()
        h4=QtWidgets.QHBoxLayout()
        h5=QtWidgets.QHBoxLayout()
        self.label_3 = QLabel(ttsapiform)
        self.label_3.setObjectName("label_3")
        self.extra = QLineEdit(ttsapiform)
        self.extra.setObjectName("extra")
        self.extra.setMinimumSize(QSize(0, 35))


        self.label_4 = QLabel(ttsapiform)
        self.label_4.setObjectName("label_4")
        self.label_4.setText("语言，默认auto" if config.defaulelang=='zh' else 'Language')
        self.language_boost = QtWidgets.QComboBox(ttsapiform)
        self.language_boost.setObjectName("language_boost")
        self.language_boost.setMinimumSize(QSize(0, 35))
        self.language_boost.addItems(['auto','Chinese', 'Chinese,Yue', 'English', 'Arabic', 'Russian', 'Spanish', 'French', 'Portuguese', 'German', 'Turkish', 'Dutch', 'Ukrainian', 'Vietnamese', 'Indonesian', 'Japanese', 'Italian', 'Korean'])
        
        label_5=QLabel(ttsapiform)
        label_5.setText('情绪' if config.defaulelang=='zh' else 'Emotion')
        self.emotion = QtWidgets.QComboBox(ttsapiform)
        self.language_boost.setObjectName("emotion")
        self.emotion.setMinimumSize(QSize(0, 35))
        self.emotion.addItems(["happy", "sad", "angry", "fearful", "disgusted", "surprised", "neutral"])
        
        
        
        h3.addWidget(self.label_3)
        h3.addWidget(self.extra)
        h4.addWidget(self.label_4)
        h4.addWidget(self.language_boost)
        h5.addWidget(label_5)
        h5.addWidget(self.emotion)
        v1.addLayout(h3)
        v1.addLayout(h4)
        v1.addLayout(h5)


        self.tips = QPlainTextEdit(ttsapiform)
        self.tips.setObjectName("tips")
        self.tips.setReadOnly(True)
        v1.addWidget(self.tips)


        h4=QtWidgets.QHBoxLayout()
        self.save = QPushButton(ttsapiform)
        self.save.setObjectName("save")
        self.save.setMinimumSize(QSize(0, 35))
        self.test = QPushButton(ttsapiform)
        self.test.setObjectName("test")
        self.test.setMinimumSize(QSize(0, 35))

        help_btn = QtWidgets.QPushButton()
        help_btn.setMinimumSize(QtCore.QSize(0, 35))
        help_btn.setStyleSheet("background-color: rgba(255, 255, 255,0)")
        help_btn.setObjectName("help_btn")
        help_btn.setCursor(Qt.PointingHandCursor)
        help_btn.setText("查看填写教程" if config.defaulelang == 'zh' else "Fill out the tutorial")
        help_btn.clicked.connect(lambda: tools.open_url(url='https://pyvideotrans.com/ttsapi'))

        h4.addWidget(self.save)
        h4.addWidget(self.test)
        h4.addWidget(help_btn)
        v1.addLayout(h4)


        self.retranslateUi(ttsapiform)

        QMetaObject.connectSlotsByName(ttsapiform)

    # setupUi

    def retranslateUi(self, ttsapiform):
        if config.defaulelang == 'zh':
            tips = """
将以POST请求向填写的API地址发送application/www-urlencode数据：

text:需要合成的文本/字符串
language:文字所属语言代码(zh-cn,zh-tw,en,ja,ko,ru,de,fr,tr,th,vi,ar,hi,hu,es,pt,it)/字符串
voice:配音角色名称/字符串
rate:加减速值，0或者 '+数字%' '-数字%'，代表在正常速度基础上进行加减速的百分比/字符串
ostype:win32或mac或linux操作系统类型/字符串
extra:额外参数/字符串

期待从接口返回json格式数据：
{
    code:0=合成成功时，>0的数字代表失败
    msg:ok=合成成功时，其他为失败原因
    data:在合成成功时，返回mp3文件的完整url地址，用于在软件内下载。失败时为空
}       

     
"""
        else:
            tips = """
            
The application/www-urlencode data will be sent in a POST request to the filled API address:

text:text/string
language:language code(zh-cn,zh-tw,en,ja,ko,ru,de,fr,tr,th,vi,ar,hi,hu,es,pt,it) / string
voice:voice character name/string
rate:acceleration/deceleration value, 0 or '+numeric%' '-numeric%', represents the percentage of acceleration/deceleration on top of the normal speed /string
ostype:win32 or mac or linux OS type/string
extra:extra parameters/string

Expect data to be returned from the interface in json format:
{
    code:0=when synthesis is successful, a number >0 means failure
    msg:ok=when the synthesis was successful, other is the reason for failure
    data:On successful synthesis, return the full url of the mp3 file for downloading within the software. When it fails, the url will be empty.
}            
"""
        ttsapiform.setWindowTitle("自定义TTS-API/无编码能力勿使用该功能" if config.defaulelang == 'zh' else "Customizing the TTS-API")
        self.label_3.setText("密钥SK" if config.defaulelang == 'zh' else "SK")
        self.tips.setPlainText(tips)
        self.tips.setPlaceholderText("")
        self.save.setText("保存" if config.defaulelang == 'zh' else "Save")
        self.api_url.setPlaceholderText(
            "填写http开头的完整地址" if config.defaulelang == 'zh' else "Fill in the full address starting with http")
        self.label.setText("自定义TTS API" if config.defaulelang == 'zh' else "Customizing TTS-API")
        self.voice_role.setPlaceholderText("填写可用的配音角色名称，以英文逗号分隔多个" if config.defaulelang == 'zh' else "")
        self.label_2.setText(
            "配音角色名称" if config.defaulelang == 'zh' else "Fill in the names of the available voiceover characters, separating multiple ones with English commas")
        self.extra.setPlaceholderText(
            "填写通过extra键向api传递的额外参数，为空则传递pyvideotrans" if config.defaulelang == 'zh' else "Fill in the extra parameters passed to the api via the extra key, null to pass pyvideotrans")
        self.test.setText("测试Api" if config.defaulelang == 'zh' else "Test API")
    # retranslateUi
