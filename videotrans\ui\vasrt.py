# run again.  Do not edit this file unless you know what you are doing.

from pathlib import Path

from PySide6 import QtCore, QtWidgets
from PySide6.QtCore import QMetaObject,Qt, QTime, QTimer, QSize, QEvent
from PySide6.QtWidgets import QHBoxLayout,QFontDialog,QColorDialog, QTimeEdit
from PySide6.QtGui import QFont, QColor, QDragEnterEvent, QDropEvent

from videotrans.configure import config


class Ui_vasrt(object):
    def setupUi(self, vasrt):
        self.has_done = False
        if not vasrt.objectName():
            vasrt.setObjectName(u"vasrt")
        vasrt.resize(800, 500)
        vasrt.setWindowModality(QtCore.Qt.NonModal)

        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(vasrt.sizePolicy().hasHeightForWidth())
        vasrt.setSizePolicy(sizePolicy)

        self.horizontalLayout_3 = QHBoxLayout(vasrt)
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")

        # start
        self.v3 = QtWidgets.QVBoxLayout()
        self.v3.setObjectName("v3")

        # h3
        self.h3 = QtWidgets.QHBoxLayout()
        self.h3.setObjectName("horizontalLayout_3")
        self.label_4 = QtWidgets.QLabel()
        self.label_4.setSizePolicy(sizePolicy)
        self.label_4.setMinimumSize(QtCore.QSize(100, 40))
        self.label_4.setAlignment(QtCore.Qt.AlignLeading | QtCore.Qt.AlignLeft | QtCore.Qt.AlignVCenter)
        self.label_4.setObjectName("label_4")
        self.h3.addWidget(self.label_4, 0, QtCore.Qt.AlignTop)

        self.ysphb_videoinput = QtWidgets.QLineEdit()
        self.ysphb_videoinput.setMinimumSize(QtCore.QSize(0, 40))
        self.ysphb_videoinput.setAlignment(QtCore.Qt.AlignLeading | QtCore.Qt.AlignLeft | QtCore.Qt.AlignVCenter)
        self.ysphb_videoinput.setReadOnly(True)
        self.ysphb_videoinput.setObjectName("ysphb_videoinput")
        self.h3.addWidget(self.ysphb_videoinput, 0, QtCore.Qt.AlignTop)

        self.ysphb_selectvideo = QtWidgets.QPushButton()
        self.ysphb_selectvideo.setMinimumSize(QtCore.QSize(150, 40))
        self.ysphb_selectvideo.setObjectName("ysphb_selectvideo")
        self.h3.addWidget(self.ysphb_selectvideo, 0, QtCore.Qt.AlignTop)

        # v3 add h3
        self.v3.addLayout(self.h3)

        # h5
        self.h5 = QtWidgets.QHBoxLayout()
        self.h5.setObjectName("horizontalLayout_5")
        self.label_5 = QtWidgets.QLabel()
        self.label_5.setMinimumSize(QtCore.QSize(100, 40))
        self.label_5.setObjectName("label_5")
        self.h5.addWidget(self.label_5, 0, QtCore.Qt.AlignTop)

        self.ysphb_wavinput = QtWidgets.QLineEdit()
        self.ysphb_wavinput.setMinimumSize(QtCore.QSize(0, 40))
        self.ysphb_wavinput.setObjectName("ysphb_wavinput")
        self.h5.addWidget(self.ysphb_wavinput, 0, QtCore.Qt.AlignTop)

        self.ysphb_wavinput.textChanged.connect(self.remainraw)

        self.ysphb_selectwav = QtWidgets.QPushButton()
        self.ysphb_selectwav.setMinimumSize(QtCore.QSize(150, 40))
        self.ysphb_selectwav.setObjectName("ysphb_selectwav")
        self.h5.addWidget(self.ysphb_selectwav, 0, QtCore.Qt.AlignTop)
        self.v3.addLayout(self.h5)

        # h6
        self.h6 = QtWidgets.QHBoxLayout()
        self.h6.setObjectName("h6")

        self.label_6 = QtWidgets.QLabel()
        self.label_6.setMinimumSize(QtCore.QSize(100, 40))
        self.label_6.setObjectName("label_6")
        self.h6.addWidget(self.label_6, 0, QtCore.Qt.AlignTop)
        self.ysphb_srtinput = QtWidgets.QLineEdit()
        self.ysphb_srtinput.setMinimumSize(QtCore.QSize(0, 40))
        self.ysphb_srtinput.setObjectName("ysphb_srtinput")

        self.h6.addWidget(self.ysphb_srtinput, 0, QtCore.Qt.AlignTop)
        self.ysphb_selectsrt = QtWidgets.QPushButton()
        self.ysphb_selectsrt.setMinimumSize(QtCore.QSize(150, 40))
        self.ysphb_selectsrt.setObjectName("ysphb_selectsrt")
        self.h6.addWidget(self.ysphb_selectsrt, 0, QtCore.Qt.AlignTop)

        self.h7 = QtWidgets.QHBoxLayout()
        self.h7.setObjectName("h7")
        self.ysphb_replace = QtWidgets.QCheckBox()
        self.ysphb_replace.setObjectName("ysphb_replace")
        self.ysphb_replace.setDisabled(True)
        self.ysphb_replace.setText(config.transobj['Preserve the original sound in the video'])

        label_audio = QtWidgets.QLabel()
        label_audio.setText("音频时长大于视频时" if config.defaulelang == 'zh' else "Audio duration > video")
        self.audio_process = QtWidgets.QComboBox()
        self.audio_process.addItems([
            "截断" if config.defaulelang == 'zh' else "Truncate",
            "音频加速" if config.defaulelang == 'zh' else "Auto Accelerate",
            "视频末尾定格" if config.defaulelang == 'zh' else "Video copy",
        ])


        self.ysphb_maxlenlabel = QtWidgets.QLabel()
        self.ysphb_maxlenlabel.setText("硬字幕单行字符数")
        self.ysphb_maxlen = QtWidgets.QLineEdit()
        self.ysphb_maxlen.setText('30')

        self.layout_form0 = QtWidgets.QFormLayout()
        self.layout_form0.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.ysphb_maxlenlabel)
        self.layout_form0.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.ysphb_maxlen)

        self.ysphb_issoft = QtWidgets.QCheckBox()
        self.ysphb_issoft.setObjectName("ysphb_issoft")
        self.ysphb_issoft.setChecked(False)
        self.ysphb_issoft.setText('嵌入软字幕' if config.defaulelang == 'zh' else 'Embedded Soft Subtitles')

        self.layout_form = QtWidgets.QFormLayout()

        self.languagelabel = QtWidgets.QLabel()
        self.languagelabel.setText('软字幕语言' if config.defaulelang == 'zh' else 'soft subtitle language')
        self.languagelabel.setStyleSheet('color:#777')
        self.language = QtWidgets.QComboBox()
        self.language.setMinimumSize(QtCore.QSize(0, 30))
        self.language.setObjectName("language")
        self.language.addItems(config.langnamelist)
        self.language.setDisabled(True)
        self.ysphb_issoft.toggled.connect(self.update_language)

        self.layout_form.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.languagelabel)
        self.layout_form.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.language)

        self.h7.addWidget(self.ysphb_replace)
        self.h7.addWidget(label_audio)
        self.h7.addWidget(self.audio_process)
        self.h7.addLayout(self.layout_form0)
        self.h7.addStretch()
        self.h7.addWidget(self.ysphb_issoft)
        self.h7.addLayout(self.layout_form)

        self.v3.addLayout(self.h6)
        self.v3.addLayout(self.h7)


        self.font_button = QtWidgets.QPushButton("选择字体" if config.defaulelang == 'zh' else 'Select Fonts')
        self.font_button.setToolTip('点击选择字体' if config.defaulelang == 'zh' else 'Click it for select fonts')
        self.font_button.clicked.connect(self.choose_font)
        self.font_button.setCursor(Qt.PointingHandCursor)

        self.color_button = QtWidgets.QPushButton("字体颜色" if config.defaulelang == 'zh' else 'Text Colors')
        self.color_button.setCursor(Qt.PointingHandCursor)
        self.color_button.clicked.connect(self.choose_color)

        self.backgroundcolor_button = QtWidgets.QPushButton("背景色" if config.defaulelang == 'zh' else 'Backgroud Colors')
        self.backgroundcolor_button.setCursor(Qt.PointingHandCursor)
        self.backgroundcolor_button.clicked.connect(self.choose_backgroundcolor)
        self.backgroundcolor_button.setToolTip(
            '不同播放器下可能不起作用' if config.defaulelang == 'zh' else 'May not work in different players')

        self.bordercolor_button = QtWidgets.QPushButton("边框色" if config.defaulelang == 'zh' else 'Backgroud Colors')
        self.bordercolor_button.setCursor(Qt.PointingHandCursor)
        self.bordercolor_button.clicked.connect(self.choose_bordercolor)
        self.bordercolor_button.setToolTip(
            '不同播放器下可能不起作用' if config.defaulelang == 'zh' else 'May not work in different players')

        self.font_size_edit = QtWidgets.QLineEdit()
        self.font_size_edit.setFixedWidth(80)
        self.font_size_edit.setText('16')
        self.font_size_edit.setPlaceholderText("字体大小" if config.defaulelang == 'zh' else 'Font Size')
        self.font_size_edit.setToolTip("字体大小" if config.defaulelang == 'zh' else 'Font Size')

        # 初始化字体和颜色
        self.selected_font = QFont('Arial', 16)  # 默认字体
        self.selected_color = QColor('#00FFFFFF')  # 默认颜色
        self.selected_backgroundcolor = QColor('#00000000')  # 默认颜色
        self.selected_bordercolor = QColor('#00000000')  # 默认颜色
        
        
        self.ysphb_borderstyle = QtWidgets.QCheckBox()
        self.ysphb_borderstyle.setObjectName("ysphb_borderstyle")
        self.ysphb_borderstyle.setChecked(False)
        self.ysphb_borderstyle.setToolTip('若选中则文字背后有纯色背景块，不选中则文字周围有描边和阴影'if config.defaulelang == 'zh' else 'If selected, there will be a solid color background behind the text. If not selected, there will be a stroke and shadow around the text.')
        self.ysphb_borderstyle.setText('背景风格' if config.defaulelang == 'zh' else  'Background Style')

        format_layout = QHBoxLayout()
        format_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)

        format_layout.addWidget(self.font_button)
        format_layout.addWidget(self.font_size_edit)
        format_layout.addWidget(self.color_button)
        format_layout.addWidget(self.backgroundcolor_button)
        format_layout.addWidget(self.bordercolor_button)
        format_layout.addWidget(self.ysphb_borderstyle)

        self.v3.addLayout(format_layout)

        self.ysphb_startbtn = QtWidgets.QPushButton()
        self.ysphb_startbtn.setMinimumSize(QtCore.QSize(250, 40))
        self.ysphb_startbtn.setObjectName("ysphb_startbtn")
        self.v3.addWidget(self.ysphb_startbtn)
        self.v3.addStretch()

        self.h8 = QtWidgets.QHBoxLayout()
        self.h8.setObjectName("horizontalLayout_20")
        self.ysphb_out = QtWidgets.QLineEdit()
        self.ysphb_out.setMinimumSize(QtCore.QSize(0, 30))
        self.ysphb_out.setReadOnly(True)
        self.ysphb_out.setObjectName("ysphb_out")
        self.h8.addWidget(self.ysphb_out)
        self.ysphb_opendir = QtWidgets.QPushButton()
        self.ysphb_opendir.setMinimumSize(QtCore.QSize(0, 30))
        self.ysphb_opendir.setObjectName("ysphb_opendir")
        self.h8.addWidget(self.ysphb_opendir)
        self.v3.addLayout(self.h8)

        # end
        self.horizontalLayout_3.addLayout(self.v3)

        self.retranslateUi(vasrt)

        QMetaObject.connectSlotsByName(vasrt)

    def qcolor_to_ass_color(self, color, type='fc'):    
        # 获取颜色的 RGB 值
        r = color.red()
        g = color.green()
        b = color.blue()
        if type in ['bg', 'bd']:
            return f"&H80{b:02X}{g:02X}{r:02X}"
        # 将 RGBA 转换为 ASS 的颜色格式 &HBBGGRR
        a = color.alpha()
        return f"&H{a:02X}{b:02X}{g:02X}{r:02X}"
        #return f"&H{b:02X}{g:02X}{r:02X}"

    def choose_font(self):

        dialog = QFontDialog(self.selected_font, self)
        if dialog.exec():
            font = dialog.selectedFont()
            font_name = font.family()
            font_size = font.pointSize()
            self.selected_font = font
            self.font_size_edit.setText(str(font_size))
            self.font_button.setText(font_name)
            self._setfont()

    def _setfont(self):
        bgcolor_name = self.selected_backgroundcolor.name()
        bgcolor = '' if self.selected_backgroundcolor.alpha() == 255 else f'background-color:{bgcolor_name}'
        bdcolor_name = self.selected_bordercolor.name()
        bdcolor = '' if self.selected_bordercolor.alpha() == 255 else f'border:1px solid {bdcolor_name}'
        # color_name = self.selected_color.name()
        def blend_colors(selected_color: QColor, bgcolor_name: str) -> str:
            bg_color = QColor(bgcolor_name)
            # Invert alpha: 0 = solid color, 255 = transparent
            inv_alpha = 1.0 - (selected_color.alpha() / 255.0)

            r = int(selected_color.red() * inv_alpha + bg_color.red() * (1 - inv_alpha))
            g = int(selected_color.green() * inv_alpha + bg_color.green() * (1 - inv_alpha))
            b = int(selected_color.blue() * inv_alpha + bg_color.blue() * (1 - inv_alpha))
            # For CSS alpha: 0 (transparent) when selected_color.alpha()==255, 1 (opaque) when ==0
            css_alpha = inv_alpha

            return f'rgba({r}, {g}, {b}, {css_alpha:.2f})'
        # color = f'color:{bgcolor_name}' if self.selected_color.alpha()  == 255 else f'color:{color_name}'
        color_name = blend_colors(self.selected_color, bgcolor_name)
        color = f'color:{color_name}'
        font = self.selected_font
        button_style = f"""font-family:'{font.family()}';font-size:{font.pointSize()}px;font-weight:{700 if font.bold() else 400};font-style:{'italic' if font.italic() else 'normal'};{bgcolor};{color};{bdcolor}"""
        self.font_button.setStyleSheet(button_style)




    def choose_color(self):
        color = self.selected_color
        color.setAlpha(0)  # Set default alpha to 0 (fully opaque)
        dialog = QColorDialog(color, self)
        dialog.setOption(QColorDialog.ShowAlphaChannel, True)  # 启用透明度选择
        dialog.exec()  # or dialog.open()
        color = dialog.currentColor()
        if color.isValid():
            self.selected_color = color
            self._setfont()


    def choose_backgroundcolor(self):
        color = self.selected_backgroundcolor
        color.setAlpha(0)  # Set default alpha to 0 (fully opaque)
        dialog = QColorDialog(color, self)
        dialog.setOption(QColorDialog.ShowAlphaChannel, True)  # 启用透明度选择

        dialog.exec()  # or dialog.open() to show non-model dialog to enable alpha setting
        color = dialog.currentColor()
        print(f'{color=}')
        if color.isValid():
            self.selected_backgroundcolor = color
            self._setfont()



    def choose_bordercolor(self):

        color = self.selected_bordercolor
        #color.setAlpha(255)  # Set default alpha to 255 (fully transparent)
        dialog = QColorDialog(color, self)
        dialog.setOption(QColorDialog.ShowAlphaChannel, True)  # 启用透明度选择

        dialog.exec()  # or dialog.open()
        color = dialog.currentColor()
        if color.isValid():
            self.selected_bordercolor = color
            self._setfont()



    def remainraw(self, t):
        if Path(t).is_file():
            self.ysphb_replace.setDisabled(False)
            self.ysphb_replace.setChecked(True)
        else:
            self.ysphb_replace.setChecked(False)
            self.ysphb_replace.setDisabled(True)

    def update_language(self, state):
        self.languagelabel.setStyleSheet(f"""color:#f1f1f1""" if state else 'color:#777777')
        self.language.setDisabled(False if state else True)

        self.font_button.setDisabled(True if state else False)
        self.font_size_edit.setDisabled(True if state else False)
        self.color_button.setDisabled(True if state else False)
        self.backgroundcolor_button.setDisabled(True if state else False)
        self.bordercolor_button.setDisabled(True if state else False)

    def retranslateUi(self, vasrt):
        vasrt.setWindowTitle("视频、音频、字幕三者合并" if config.defaulelang == 'zh' else 'Video, audio, and subtitle merging')

        self.label_4.setText('视频文件' if config.defaulelang == 'zh' else 'Video')
        self.label_5.setText('音频文件' if config.defaulelang == 'zh' else 'Audio')
        self.label_6.setText('字幕文件/srt' if config.defaulelang == 'zh' else 'Subtitles/srt')
        self.ysphb_selectvideo.setText('选择视频文件' if config.defaulelang == 'zh' else 'Select a Video')
        self.ysphb_videoinput.setPlaceholderText('选择视频文件' if config.defaulelang == 'zh' else 'Select a Video')
        self.ysphb_selectwav.setText('选择音频文件' if config.defaulelang == 'zh' else 'Select a Audio')
        self.ysphb_wavinput.setPlaceholderText('选择音频文件' if config.defaulelang == 'zh' else 'Select a Audio')
        self.ysphb_selectsrt.setText('选择srt字幕文件' if config.defaulelang == 'zh' else 'Select a Srt file')
        self.ysphb_srtinput.setPlaceholderText('选择srt字幕文件' if config.defaulelang == 'zh' else 'Select a Srt file')
        self.ysphb_startbtn.setText('开始执行' if config.defaulelang == 'zh' else 'Start operating')
        self.ysphb_opendir.setText('打开结果目录' if config.defaulelang == 'zh' else 'Open the results catalog')
