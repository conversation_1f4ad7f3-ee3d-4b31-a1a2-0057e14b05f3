# run again.  Do not edit this file unless you know what you are doing.


from PySide6 import QtCore, QtWidgets
from PySide6.QtCore import Qt
from PySide6.QtWidgets import QPlainTextEdit

from videotrans.configure import config
from videotrans.util import tools


class Ui_senseform(object):
    def setupUi(self, senseform):
        self.has_done = False
        senseform.setObjectName("senseform")
        senseform.setWindowModality(QtCore.Qt.NonModal)
        senseform.resize(500, 300)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(senseform.sizePolicy().hasHeightForWidth())
        senseform.setSizePolicy(sizePolicy)

        self.verticalLayout = QtWidgets.QVBoxLayout(senseform)
        self.verticalLayout.setObjectName("verticalLayout")
        self.verticalLayout.setAlignment(QtCore.Qt.AlignmentFlag.AlignTop)

        self.formLayout_2 = QtWidgets.QFormLayout()
        self.formLayout_2.setSizeConstraint(QtWidgets.QLayout.SetMinimumSize)
        self.formLayout_2.setFormAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.formLayout_2.setObjectName("formLayout_2")
        self.label = QtWidgets.QLabel(senseform)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label.sizePolicy().hasHeightForWidth())
        self.label.setSizePolicy(sizePolicy)
        self.label.setMinimumSize(QtCore.QSize(100, 35))
        self.label.setAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.label.setObjectName("label")

        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label)
        self.sense_url = QtWidgets.QLineEdit(senseform)
        self.sense_url.setMinimumSize(QtCore.QSize(0, 35))
        self.sense_url.setObjectName("sense_url")

        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.sense_url)
        self.verticalLayout.addLayout(self.formLayout_2)




        self.set = QtWidgets.QPushButton(senseform)
        self.set.setMinimumSize(QtCore.QSize(0, 35))
        self.set.setObjectName("set")

        self.test = QtWidgets.QPushButton(senseform)
        self.test.setMinimumSize(QtCore.QSize(0, 30))
        self.test.setObjectName("test")

        help_btn = QtWidgets.QPushButton()
        help_btn.setMinimumSize(QtCore.QSize(0, 35))
        help_btn.setStyleSheet("background-color: rgba(255, 255, 255,0)")
        help_btn.setObjectName("help_btn")
        help_btn.setCursor(Qt.PointingHandCursor)
        help_btn.setText("查看填写教程" if config.defaulelang == 'zh' else "Fill out the tutorial")
        help_btn.clicked.connect(lambda: tools.open_url(url='https://pyvideotrans.com/sensevoice'))


        self.layout_btn = QtWidgets.QHBoxLayout()
        self.layout_btn.setObjectName("layout_btn")


        self.layout_btn.addWidget(self.set)
        self.layout_btn.addWidget(self.test)
        self.layout_btn.addWidget(help_btn)

        self.verticalLayout.addLayout(self.layout_btn)

        self.retranslateUi(senseform)
        QtCore.QMetaObject.connectSlotsByName(senseform)

    def retranslateUi(self, senseform):
        senseform.setWindowTitle("SenseVoice语音识别API" if config.defaulelang == 'zh' else 'SenseVoice Speech Recognition API')

        self.label.setText('API地址' if config.defaulelang=='zh' else 'API URL')
        self.sense_url.setPlaceholderText('Api url')
        self.set.setText('保存' if config.defaulelang == 'zh' else 'Save')
        self.test.setText('测试' if config.defaulelang == 'zh' else 'Test')
