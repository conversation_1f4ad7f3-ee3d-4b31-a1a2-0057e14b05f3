{"ar": ["No", "ar-AE-FatimaNeural", "ar-AE-HamdanNeural", "ar-BH-<PERSON><PERSON>eural", "ar-BH-AliNeural", "ar-DZ-AminaNeural", "ar-DZ-Ismael<PERSON>eural", "ar-EG-SalmaNeural", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ar-IQ-BasselNeural", "ar-JO-SanaNeural", "ar-JO-TaimNeural", "ar-KW-NouraNeural", "ar-KW-FahedNeural", "ar-LB-<PERSON><PERSON><PERSON><PERSON>", "ar-LB-RamiNeural", "ar-LY-ImanNeural", "ar-<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "ar-<PERSON><PERSON><PERSON><PERSON>N<PERSON><PERSON>", "ar-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ar-OM-AyshaNeural", "ar-O<PERSON><PERSON>Abdullah<PERSON><PERSON><PERSON>", "ar-QA-AmalNeural", "ar-QA-MoazNeural", "ar-SA-ZariyahNeural", "ar-SA-HamedNeural", "ar-SY-AmanyNeural", "ar-S<PERSON>-<PERSON>thNeural", "ar-TN-ReemNeural", "ar-TN-HediNeural", "ar-<PERSON><PERSON><PERSON><PERSON><PERSON>", "ar-YE-SalehNeural"], "bn": ["No", "bn-BD-NabanitaNeural", "bn-BD-<PERSON><PERSON>epNeural", "bn-IN-<PERSON><PERSON>aaNeural", "bn-IN-BashkarNeural"], "cs": ["No", "cs-CZ-VlastaNeural", "cs-CZ-<PERSON>inNeural"], "de": ["No", "de-AT-IngridNeural", "de-AT-JonasNeural", "de-CH-LeniNeural", "de-CH-Jan<PERSON><PERSON><PERSON>", "de-DE-Katja<PERSON>eural", "de-DE-ConradNeural", "de-DE-SeraphinaMultilingualNeural", "de-DE-FlorianMultilingualNeural", "de-DE-AmalaNeural", "de-DE-BerndNeural", "de-DE-ChristophNeural", "de-DE-ElkeNeural", "de-DE-GiselaNeural", "de-DE-KasperNeural", "de-DE-<PERSON><PERSON><PERSON>", "de-DE-KlarissaNeural", "de-DE-KlausN<PERSON>", "de-DE-LouisaNeural", "de-DE-Maj<PERSON><PERSON><PERSON>", "de-DE-RalfNeural", "de-DE-TanjaNeural", "de-DE-Seraphina:DragonHDLatestNeural"], "en": ["No", "en-AU-<PERSON><PERSON><PERSON><PERSON>", "en-AU-<PERSON><PERSON><PERSON><PERSON>", "en-AU-<PERSON><PERSON><PERSON><PERSON>", "en-AU-CarlyN<PERSON><PERSON>", "en-AU-<PERSON><PERSON><PERSON><PERSON>", "en-AU-<PERSON><PERSON><PERSON><PERSON>", "en-AU-ElsieN<PERSON><PERSON>", "en-AU-Freya<PERSON>eural", "en-AU-<PERSON><PERSON><PERSON><PERSON>", "en-AU-KenN<PERSON>al", "en-AU-Kim<PERSON><PERSON><PERSON>", "en-AU-<PERSON><PERSON><PERSON><PERSON>", "en-AU-<PERSON><PERSON><PERSON><PERSON>", "en-AU-<PERSON><PERSON><PERSON><PERSON>", "en-CA-ClaraNeural", "en-CA-<PERSON><PERSON><PERSON><PERSON>", "en-GB-<PERSON><PERSON><PERSON><PERSON>", "en-GB-RyanN<PERSON><PERSON>", "en-GB-LibbyNeural", "en-GB-AdaMultilingualNeural", "en-GB-OllieMultilingualNeural", "en-GB-AbbiNeural", "en-GB-AlfieNeural", "en-GB-BellaNeural", "en-GB-<PERSON><PERSON><PERSON><PERSON>", "en-GB-EthanNeural", "en-GB-HollieNeural", "en-GB-<PERSON><PERSON><PERSON>", "en-GB-NoahNeural", "en-GB-OliverNeural", "en-GB-<PERSON><PERSON><PERSON><PERSON>", "en-GB-<PERSON><PERSON><PERSON><PERSON>", "en-GB-MiaNeural", "en-HK-YanNeural", "en-HK-SamNeural", "en-IE-<PERSON><PERSON><PERSON><PERSON>", "en-IE-Connor<PERSON><PERSON><PERSON>", "en-IN-AaravNeural", "en-IN-AashiNeural", "en-IN-AnanyaNeural", "en-IN-Ka<PERSON>aNeural", "en-IN-KunalNeural", "en-IN-NeerjaNeural", "en-IN-PrabhatNeural", "en-IN-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "en-KE-AsiliaNeural", "en-KE-ChilembaNeural", "en-NG-EzinneNeural", "en-NG-AbeoNeural", "en-NZ-<PERSON><PERSON><PERSON><PERSON>", "en-NZ-MitchellNeural", "en-PH-RosaNeural", "en-PH-<PERSON><PERSON><PERSON><PERSON>", "en-SG-LunaNeural", "en-SG-Wayne<PERSON><PERSON><PERSON>", "en-TZ-ImaniNeural", "en-TZ-ElimuNeural", "en-US-AvaMultilingualNeural", "en-US-AndrewMultilingualNeural", "en-US-EmmaMultilingualNeural", "en-US-BrianMultilingualNeural", "en-US-AvaNeural", "en-US-<PERSON><PERSON><PERSON><PERSON>", "en-US-<PERSON><PERSON><PERSON><PERSON>", "en-US-<PERSON><PERSON><PERSON><PERSON>", "en-US-<PERSON><PERSON><PERSON><PERSON>", "en-US-GuyN<PERSON><PERSON>", "en-US-AriaNeural", "en-US-DavisNeural", "en-US-<PERSON><PERSON><PERSON><PERSON>", "en-US-<PERSON><PERSON><PERSON><PERSON>", "en-US-KaiNeural", "en-US-LunaNeural", "en-US-SaraNeural", "en-US-<PERSON><PERSON><PERSON><PERSON>", "en-US-NancyNeural", "en-US-CoraMultilingualNeural", "en-US-ChristopherMultilingualNeural", "en-US-BrandonMultilingualNeural", "en-US-AmberN<PERSON><PERSON>", "en-US-AnaNeural", "en-US-<PERSON><PERSON><PERSON><PERSON>", "en-US-BrandonN<PERSON><PERSON>", "en-US-<PERSON><PERSON><PERSON>", "en-US-CoraNeural", "en-US-<PERSON><PERSON><PERSON><PERSON>", "en-US-<PERSON><PERSON><PERSON><PERSON>", "en-US-<PERSON><PERSON><PERSON><PERSON>", "en-US-JennyMultilingualNeural", "en-US-<PERSON><PERSON>", "en-US-<PERSON><PERSON><PERSON><PERSON>", "en-US-<PERSON><PERSON><PERSON><PERSON>", "en-US-RyanMultilingualNeural", "en-US-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "en-US-SteffanMultilingualNeural", "en-US-Andrew:DragonHDLatestNeural", "en-US-Andrew2:DragonHDLatestNeural", "en-US-Aria:DragonHDLatestNeural", "en-US-Ava:DragonHDLatestNeural", "en-US-Brian:DragonHDLatestNeural", "en-US-Davis:DragonHDLatestNeural", "en-US-Emma:DragonHDLatestNeural", "en-US-Emma2:DragonHDLatestNeural", "en-US-Jenny:DragonHDLatestNeural", "en-US-<PERSON><PERSON><PERSON>:DragonHDLatestNeural", "en-ZA-LeahNeural", "en-ZA-LukeNeural"], "es": ["No", "es-AR-ElenaNeural", "es-AR-TomasNeural", "es-BO-SofiaNeural", "es-BO-MarceloNeural", "es-CL-CatalinaNeural", "es-CL-LorenzoNeural", "es-CO-SalomeNeural", "es-CO-GonzaloNeural", "es-CR-MariaNeural", "es-CR-JuanNeural", "es-CU-BelkysNeural", "es-CU-ManuelNeural", "es-DO-RamonaNeural", "es-DO-EmilioNeural", "es-EC-AndreaNeural", "es-EC-LuisNeural", "es-ES-Elvira<PERSON>eural", "es-ES-AlvaroNeural", "es-ES-ArabellaMultilingualNeural", "es-ES-IsidoraMultilingualNeural", "es-ES-TristanMultilingualNeural", "es-ES-XimenaMultilingualNeural", "es-ES-AbrilNeural", "es-ES-ArnauNeural", "es-ES-DarioNeural", "es-ES-EliasNeural", "es-ES-EstrellaNeural", "es-ES-IreneNeural", "es-ES-LaiaNeural", "es-ES-LiaNeural", "es-ES-NilNeural", "es-ES-SaulNeural", "es-ES-TeoNeural", "es-ES-TrianaNeural", "es-ES-VeraNeural", "es-ES-XimenaNeural", "es-GQ-Teresa<PERSON><PERSON><PERSON>", "es-GQ-JavierNeural", "es-GT-MartaNeural", "es-GT-AndresNeural", "es-HN-Karla<PERSON>eural", "es-HN-CarlosNeural", "es-MX-DaliaNeural", "es-MX-JorgeNeural", "es-MX-BeatrizNeural", "es-MX-CandelaNeural", "es-MX-CarlotaNeural", "es-MX-CecilioNeural", "es-MX-Gerardo<PERSON>eural", "es-MX-LarissaNeural", "es-MX-Liberto<PERSON>eural", "es-MX-LucianoNeural", "es-MX-MarinaNeural", "es-MX-NuriaNeural", "es-MX-PelayoNeural", "es-MX-RenataNeural", "es-MX-YagoNeural", "es-NI-Yo<PERSON>aNeural", "es-NI-FedericoNeural", "es-PA-MargaritaNeural", "es-PA-RobertoNeural", "es-PE-CamilaNeural", "es-PE-AlexNeural", "es-PR-Karin<PERSON>", "es-PR-VictorNeural", "es-PY-TaniaNeural", "es-PY-<PERSON>", "es-SV-LorenaNeural", "es-SV-RodrigoNeural", "es-US-PalomaNeural", "es-US-AlonsoNeural", "es-UY-ValentinaNeural", "es-UY-MateoNeural", "es-VE-PaolaNeural", "es-VE-SebastianNeural"], "fr": ["No", "fr-BE-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fr-B<PERSON>-<PERSON><PERSON>", "fr-CA-<PERSON><PERSON>vie<PERSON>eural", "fr-<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "fr-CA-<PERSON><PERSON><PERSON><PERSON>", "fr-<PERSON>-<PERSON><PERSON><PERSON>", "fr-CH-ArianeNeural", "fr-CH-FabriceNeural", "fr-FR-<PERSON><PERSON>", "fr-FR-<PERSON><PERSON><PERSON><PERSON>", "fr-FR-VivienneMultilingualNeural", "fr-FR-RemyMultilingualNeural", "fr-FR-LucienMultilingualNeural", "fr-FR-<PERSON><PERSON><PERSON>", "fr-FR-B<PERSON>itteNeural", "fr-FR-CelesteNeural", "fr-FR-<PERSON><PERSON><PERSON><PERSON>", "fr-FR-<PERSON><PERSON>", "fr-FR-EloiseNeural", "fr-FR-<PERSON><PERSON><PERSON><PERSON>", "fr-FR-<PERSON><PERSON>", "fr-FR-<PERSON><PERSON><PERSON><PERSON>", "fr-FR-<PERSON><PERSON>", "fr-FR-YvesNeural", "fr-FR-YvetteNeural"], "he": ["No", "he-IL-HilaNeural", "he-IL-AvriNeural"], "hi": ["No", "hi-IN-AaravNeural", "hi-IN-AnanyaNeural", "hi-IN-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hi-IN-KunalNeural", "hi-IN-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hi-IN-SwaraNeural", "hi-IN-<PERSON><PERSON><PERSON><PERSON><PERSON>"], "hu": ["No", "hu-HU-NoemiNeural", "hu-HU-TamasNeural"], "id": ["No", "id-ID-GadisNeural", "id-ID-ArdiNeural"], "it": ["No", "it-IT-ElsaNeural", "it-IT-IsabellaNeural", "it-IT-DiegoNeural", "it-IT-AlessioMultilingualNeural", "it-IT-IsabellaMultilingualNeural", "it-IT-GiuseppeMultilingualNeural", "it-IT-MarcelloMultilingualNeural", "it-IT-BenignoNeural", "it-IT-CalimeroNeural", "it-IT-Cat<PERSON>Neural", "it-IT-FabiolaNeural", "it-IT-FiammaNeural", "it-IT-<PERSON><PERSON><PERSON>", "it-IT-GiuseppeNeural", "it-IT-ImeldaNeural", "it-IT-IrmaNeural", "it-IT-LisandroNeural", "it-IT-PalmiraNeural", "it-IT-<PERSON><PERSON>Neural", "it-IT-RinaldoNeural"], "ja": ["No", "ja-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ja-<PERSON><PERSON><PERSON><PERSON><PERSON>", "ja-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ja-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ja-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ja-<PERSON><PERSON><PERSON><PERSON>", "ja-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ja-<PERSON><PERSON><PERSON><PERSON><PERSON>:DragonHDLatestNeural"], "kk": ["No", "kk-KZ-AigulNeural", "kk-KZ-DauletNeural"], "ko": ["No", "ko-KR-SunHiNeural", "ko-KR-InJoonNeural", "ko-KR-HyunsuMultilingualNeural", "ko-KR-BongJinNeural", "ko-KR-GookMinNeural", "ko-KR-HyunsuNeural", "ko-KR-JiMinNeural", "ko-KR-SeoHyeonNeural", "ko-KR-SoonBokNeural", "ko-KR-YuJinNeural"], "ms": ["No", "ms-<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ms-<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>"], "nl": ["No", "nl-BE-DenaNeural", "nl-BE-<PERSON><PERSON>udNeural", "nl-NL-FennaNeural", "nl-NL-MaartenNeural", "nl-NL-ColetteNeural"], "pl": ["No", "pl-PL-AgnieszkaNeural", "pl-PL-MarekNeural", "pl-PL-ZofiaNeural"], "pt": ["No", "pt-BR-FranciscaNeural", "pt-BR-AntonioNeural", "pt-BR-MacerioMultilingualNeural", "pt-BR-ThalitaMultilingualNeural", "pt-<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "pt-BR-DonatoNeural", "pt-BR-ElzaNeural", "pt-BR-<PERSON>abio<PERSON><PERSON>al", "pt-BR-Giovanna<PERSON>eural", "pt-BR-HumbertoNeural", "pt-<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "pt-BR-Leila<PERSON><PERSON><PERSON>", "pt-BR-Leticia<PERSON>eural", "pt-BR-ManuelaNeural", "pt-BR-Nicola<PERSON><PERSON><PERSON>", "pt-BR<PERSON><PERSON><PERSON><PERSON>Neural", "pt-BR-ValerioNeural", "pt-BR-YaraNeural", "pt-<PERSON>-<PERSON><PERSON>", "pt-PT-DuarteNeural", "pt-PT-FernandaNeural"], "ru": ["No", "ru-RU-<PERSON><PERSON><PERSON>", "ru-RU-<PERSON><PERSON><PERSON><PERSON>", "ru-RU-DariyaNeural"], "sv": ["No", "sv-SE-SofieNeural", "sv-SE-Mattias<PERSON>al", "sv-SE-HilleviNeural"], "th": ["No", "th-TH-<PERSON><PERSON><PERSON><PERSON>eNeural", "th-TH-NiwatNeural", "th-TH-A<PERSON>raNeural"], "tr": ["No", "tr-TR-EmelNeural", "tr-TR-AhmetNeural"], "uk": ["No", "uk-UA-PolinaNeural", "uk-UA-OstapNeural"], "vi": ["No", "vi-VN-HoaiMyNeural", "vi-VN-Nam<PERSON>inhNeural"], "zh": ["No", "zh-CN-XiaoxiaoNeural", "zh-CN-YunxiNeural", "zh-CN-YunjianNeural", "zh-CN-XiaoyiNeural", "zh-CN-YunyangNeural", "zh-CN-<PERSON><PERSON>N<PERSON>al", "zh-CN-XiaochenMultilingualNeural", "zh-C<PERSON>-<PERSON><PERSON>Neural", "zh-CN-XiaomengNeural", "zh-CN-XiaomoNeural", "zh-CN-XiaoqiuNeural", "zh-CN-XiaorouNeural", "zh-CN-XiaoruiNeural", "zh-CN-XiaoshuangNeural", "zh-CN-XiaoxiaoDialectsNeural", "zh-CN-XiaoxiaoMultilingualNeural", "zh-CN-<PERSON>yanNeural", "zh-CN-XiaoyouNeural", "zh-CN-XiaoyuMultilingualNeural", "zh-CN-XiaozhenNeural", "zh-CN-YunfengNeural", "zh-CN-YunhaoNeural", "zh-CN-YunjieNeural", "zh-CN-YunxiaNeural", "zh-CN-YunyeNeural", "zh-CN-YunyiMultilingualNeural", "zh-CN-YunzeNeural", "zh-CN-YunfanMultilingualNeural", "zh-CN-YunxiaoMultilingualNeural", "<PERSON>h-<PERSON><PERSON><PERSON><PERSON><PERSON>:DragonHDLatestNeural", "zh-<PERSON><PERSON><PERSON><PERSON><PERSON>:DragonHDLatestNeural", "zh-C<PERSON>-<PERSON><PERSON>-YundengNeural", "zh-CN-liaoning-XiaobeiNeural", "zh-CN-shaanxi-XiaoniNeural", "zh-CN-shandong-YunxiangNeural", "zh-CN-sichuan-YunxiNeural", "zh-HK-HiuMaanNeural", "zh-HK-WanLungNeural", "zh-HK-HiuGaaiNeural", "zh-TW-HsiaoChenNeural", "zh-TW-YunJheNeural", "zh-TW-HsiaoYuNeural"]}