# run again.  Do not edit this file unless you know what you are doing.


from PySide6 import QtCore, QtWidgets
from PySide6.QtCore import Qt

from videotrans.configure import config
from videotrans.util import tools


class Ui_chatgptform(object):
    def setupUi(self, chatgptform):
        self.has_done = False
        chatgptform.setObjectName("chatgptform")
        chatgptform.setWindowModality(QtCore.Qt.NonModal)
        chatgptform.resize(600, 600)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(chatgptform.sizePolicy().hasHeightForWidth())
        chatgptform.setSizePolicy(sizePolicy)
        chatgptform.setMaximumSize(QtCore.QSize(600, 600))

        v1=QtWidgets.QVBoxLayout(chatgptform)
        h1=QtWidgets.QHBoxLayout()
        h2=QtWidgets.QHBoxLayout()
        h3=QtWidgets.QHBoxLayout()
        h4=QtWidgets.QHBoxLayout()


        self.label_0 = QtWidgets.QPushButton()
        self.label_0.setGeometry(QtCore.QRect(10, 10, 580, 35))
        self.label_0.setStyleSheet("background-color: rgba(255, 255, 255,0);text-align:left")
        self.label_0.setText(
            'OpenAI ChatGPT及兼容的AI可在此使用' if config.defaulelang == 'zh' else 'AIs compatible with the ChatGPT also used here')
        v1.addWidget(self.label_0)


        self.label = QtWidgets.QLabel(chatgptform)
        self.label.setMinimumSize(QtCore.QSize(0, 35))
        self.label.setObjectName("label")
        self.chatgpt_api = QtWidgets.QLineEdit(chatgptform)
        self.chatgpt_api.setMinimumSize(QtCore.QSize(0, 35))
        self.chatgpt_api.setObjectName("chatgpt_api")
        h1.addWidget(self.label)
        h1.addWidget(self.chatgpt_api)
        v1.addLayout(h1)

        self.label_2 = QtWidgets.QLabel(chatgptform)
        self.label_2.setMinimumSize(QtCore.QSize(0, 35))
        self.label_2.setSizeIncrement(QtCore.QSize(0, 35))
        self.label_2.setObjectName("label_2")
        self.chatgpt_key = QtWidgets.QLineEdit(chatgptform)
        self.chatgpt_key.setMinimumSize(QtCore.QSize(0, 35))
        self.chatgpt_key.setObjectName("chatgpt_key")
        h2.addWidget(self.label_2)
        h2.addWidget(self.chatgpt_key)
        
        h_token=QtWidgets.QHBoxLayout()

        label_token = QtWidgets.QLabel(chatgptform)
        label_token.setObjectName("label_token")
        label_token.setText("最大输出token" if config.defaulelang == 'zh' else "Maximum output token")
        self.chatgpt_max_token = QtWidgets.QLineEdit(chatgptform)
        self.chatgpt_max_token.setMinimumSize(QtCore.QSize(0, 35))
        self.chatgpt_max_token.setObjectName("chatgpt_max_token")
        
        
        
        label_temp=QtWidgets.QLabel(chatgptform)
        label_temp.setMinimumSize(QtCore.QSize(0, 35))
        label_temp.setText("temperature")
        
        self.chatgpt_temperature=QtWidgets.QLineEdit(chatgptform)
        self.chatgpt_temperature.setObjectName("chatgpt_temperature")

        
        label_top_p=QtWidgets.QLabel(chatgptform)
        label_top_p.setMinimumSize(QtCore.QSize(0, 35))
        label_top_p.setText("top_p")
        
        self.chatgpt_top_p=QtWidgets.QLineEdit(chatgptform)
        self.chatgpt_top_p.setObjectName("chatgpt_top_p")
            
        h_token.addWidget(label_token)
        h_token.addWidget(self.chatgpt_max_token)
        
        h_token.addWidget(label_temp)
        h_token.addWidget(self.chatgpt_temperature)

        h_token.addWidget(label_top_p)
        h_token.addWidget(self.chatgpt_top_p)

           

        

        

        v1.addLayout(h2)
        v1.addLayout(h_token)

        self.label_3 = QtWidgets.QLabel(chatgptform)
        self.label_3.setObjectName("label_3")
        self.chatgpt_model = QtWidgets.QComboBox(chatgptform)
        self.chatgpt_model.setMinimumSize(QtCore.QSize(0, 35))
        self.chatgpt_model.setObjectName("chatgpt_model")
        h3.addWidget(self.label_3)
        h3.addWidget(self.chatgpt_model)
        v1.addLayout(h3)

        self.label_allmodels = QtWidgets.QLabel(chatgptform)
        self.label_allmodels.setObjectName("label_allmodels")
        self.label_allmodels.setText(
            '填写所有可用模型，以英文逗号分隔，填写后可在上方选择' if config.defaulelang == 'zh' else 'Fill in all available models, separated by commas. After filling in, you can select them above')
        v1.addWidget(self.label_allmodels)

        self.edit_allmodels = QtWidgets.QPlainTextEdit(chatgptform)
        self.edit_allmodels.setObjectName("edit_allmodels")
        v1.addWidget(self.edit_allmodels)

        self.label_4 = QtWidgets.QLabel(chatgptform)
        self.label_4.setObjectName("label_4")

        self.chatgpt_template = QtWidgets.QPlainTextEdit(chatgptform)
        self.chatgpt_template.setObjectName("chatgpt_template")
        v1.addWidget(self.label_4)
        v1.addWidget(self.chatgpt_template)


        self.set_chatgpt = QtWidgets.QPushButton(chatgptform)
        self.set_chatgpt.setMinimumSize(QtCore.QSize(0, 35))
        self.set_chatgpt.setObjectName("set_chatgpt")

        self.test_chatgpt = QtWidgets.QPushButton(chatgptform)
        self.test_chatgpt.setMinimumSize(QtCore.QSize(0, 30))
        self.test_chatgpt.setObjectName("test_chatgpt")
        help_btn = QtWidgets.QPushButton()
        help_btn.setMinimumSize(QtCore.QSize(0, 35))
        help_btn.setStyleSheet("background-color: rgba(255, 255, 255,0)")
        help_btn.setObjectName("help_btn")
        help_btn.setCursor(Qt.PointingHandCursor)
        help_btn.setText("查看填写教程" if config.defaulelang == 'zh' else "Fill out the tutorial")
        help_btn.clicked.connect(lambda: tools.open_url(url='https://pyvideotrans.com/openai'))

        h4.addWidget(self.set_chatgpt)
        h4.addWidget(self.test_chatgpt)
        h4.addWidget(help_btn)
        v1.addLayout(h4)

        self.retranslateUi(chatgptform)
        QtCore.QMetaObject.connectSlotsByName(chatgptform)

    def retranslateUi(self, chatgptform):
        chatgptform.setWindowTitle("OpenAI API 及兼容AI" if  config.defaulelang == 'zh' else "OpenAI API & Compatible AI")
        self.label_3.setText('选择模型' if config.defaulelang == 'zh' else "Model")
        self.chatgpt_template.setPlaceholderText("prompt")
        self.label_4.setText(
            "{lang}代表目标语言名称，不要删除。" if config.defaulelang == 'zh' else "{lang} represents the target language name, do not delete it.")
        self.set_chatgpt.setText('保存' if config.defaulelang == 'zh' else "Save")
        self.test_chatgpt.setText('测试..' if config.defaulelang == 'zh' else "Test..")
        self.chatgpt_api.setPlaceholderText(
            '若使用OpenAI官方接口，无需填写;第三方api在此填写' if config.defaulelang == 'zh' else 'If using the official OpenAI interface, there is no need to fill it out; Fill in the third-party API here')
        self.chatgpt_api.setToolTip(
            '若使用OpenAI官方接口，无需填写;第三方api在此填写' if config.defaulelang == 'zh' else 'If using the official OpenAI interface, there is no need to fill it out; Fill in the third-party API here')
        self.chatgpt_key.setPlaceholderText("Secret key")
        self.chatgpt_key.setToolTip(
            "必须是付费账号，免费账号频率受限无法使用" if config.defaulelang == 'zh' else 'Must be a paid account, free account frequency is limited and cannot be used')
        self.label.setText("API URL")
        self.label_2.setText("SK")
