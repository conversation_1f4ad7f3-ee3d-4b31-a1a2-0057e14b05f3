# run again.  Do not edit this file unless you know what you are doing.


from PySide6 import QtCore, QtWidgets
from PySide6.QtCore import Qt

from videotrans.configure import config
from videotrans.util import tools


class Ui_deeplform(object):
    def setupUi(self, deeplform):
        self.has_done = False
        deeplform.setObjectName("deeplform")
        deeplform.setWindowModality(QtCore.Qt.NonModal)
        deeplform.setMinimumSize(QtCore.QSize(550, 330))



        self.verticalLayout = QtWidgets.QVBoxLayout(deeplform)
        self.verticalLayout.setObjectName("verticalLayout")

        self.formLayout_2 = QtWidgets.QFormLayout()
        self.formLayout_2.setSizeConstraint(QtWidgets.QLayout.SetMinimumSize)
        self.formLayout_2.setFormAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.formLayout_2.setObjectName("formLayout_2")
        self.label = QtWidgets.QLabel(deeplform)
        self.label.setMinimumSize(QtCore.QSize(100, 35))
        self.label.setAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.label.setObjectName("label")
        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label)
        self.deepl_authkey = QtWidgets.QLineEdit(deeplform)
        self.deepl_authkey.setMinimumSize(QtCore.QSize(210, 35))
        self.deepl_authkey.setObjectName("deepl_authkey")
        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.deepl_authkey)

        self.formLayout_22 = QtWidgets.QFormLayout()
        self.formLayout_22.setSizeConstraint(QtWidgets.QLayout.SetMinimumSize)
        self.formLayout_22.setFormAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.formLayout_22.setObjectName("formLayout_22")
        self.label22 = QtWidgets.QLabel(deeplform)
        self.label22.setMinimumSize(QtCore.QSize(100, 35))
        self.label22.setAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.label22.setObjectName("label22")
        self.formLayout_22.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label22)
        self.deepl_api = QtWidgets.QLineEdit(deeplform)
        self.deepl_api.setMinimumSize(QtCore.QSize(210, 35))
        self.deepl_api.setObjectName("deepl_api")
        self.formLayout_22.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.deepl_api)

        self.formLayout_33 = QtWidgets.QFormLayout()
        self.formLayout_33.setSizeConstraint(QtWidgets.QLayout.SetMinimumSize)
        self.formLayout_33.setFormAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.formLayout_33.setObjectName("formLayout_33")
        self.label33 = QtWidgets.QLabel(deeplform)
        self.label33.setMinimumSize(QtCore.QSize(100, 35))
        self.label33.setAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.label33.setObjectName("label33")
        self.formLayout_33.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label33)
        self.deepl_gid = QtWidgets.QLineEdit(deeplform)
        self.deepl_gid.setMinimumSize(QtCore.QSize(210, 35))
        self.deepl_gid.setObjectName("deepl_gid")
        self.formLayout_33.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.deepl_gid)

        self.verticalLayout.addLayout(self.formLayout_22)
        self.verticalLayout.addLayout(self.formLayout_2)
        self.verticalLayout.addLayout(self.formLayout_33)

        self.set_deepl = QtWidgets.QPushButton(deeplform)
        self.set_deepl.setMinimumSize(QtCore.QSize(0, 35))
        self.set_deepl.setObjectName("set_deepl")

        self.test = QtWidgets.QPushButton(deeplform)
        self.test.setObjectName("test")

        help_btn = QtWidgets.QPushButton()
        help_btn.setMinimumSize(QtCore.QSize(0, 35))
        help_btn.setStyleSheet("background-color: rgba(255, 255, 255,0)")
        help_btn.setObjectName("help_btn")
        help_btn.setCursor(Qt.PointingHandCursor)
        help_btn.setText("查看填写教程" if config.defaulelang == 'zh' else "Fill out the tutorial")
        help_btn.clicked.connect(lambda: tools.open_url(url='https://pyvideotrans.com/deepl'))

        h1=QtWidgets.QHBoxLayout()
        h1.addWidget(self.set_deepl)
        h1.addWidget(self.test)
        h1.addWidget(help_btn)

        self.verticalLayout.addLayout(h1)


        self.retranslateUi(deeplform)
        QtCore.QMetaObject.connectSlotsByName(deeplform)

    def retranslateUi(self, deeplform):
        deeplform.setWindowTitle("DeepL Setting")
        self.label.setText("密钥" if config.defaulelang == 'zh' else "AUTH KEY")
        self.label22.setText("API地址" if config.defaulelang == 'zh' else "API")
        self.label33.setText("术语库id" if config.defaulelang == 'zh' else "Glossary id")
        self.deepl_gid.setPlaceholderText("填写术语库id" if config.defaulelang == 'zh' else "Glossary id")
        self.deepl_gid.setToolTip("填写术语库id" if config.defaulelang == 'zh' else "Glossary id")
        self.deepl_api.setPlaceholderText(
            "官方接口无需填写，如果使用第三方deepl接口在此填写" if config.defaulelang == 'zh' else "No need to fill in the official interface, if you use a third-party deepl interface to fill in here")
        self.set_deepl.setText('保存' if config.defaulelang == 'zh' else "Save")
        self.test.setText('测试' if config.defaulelang == 'zh' else "Test")
